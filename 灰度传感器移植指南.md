# 灰度传感器功能移植指南

## 概述

本指南将帮助您将当前工程中的灰度传感器功能移植到新的STM32项目中。该灰度传感器模块基于感为智能科技的8路灰度传感器，使用软件I2C接口通信，主要用于循迹小车的路径检测。

## 硬件要求

- **灰度传感器**: 感为智能科技8路灰度传感器模块
- **通信接口**: 软件I2C (GPIO模拟)
- **电源**: 3.3V或5V（根据传感器模块规格）
- **连接引脚**: 
  - SCL: PC4 (GRAY_SOFT_SCL_Pin)
  - SDA: PC5 (GRAY_SOFT_SDA_Pin)
- **I2C地址**: 0x4C (7位地址)

## 需要移植的文件

### 1. 核心驱动文件
```
User/Module/grayscale/
├── gw_grayscale_sensor.h    # 灰度传感器协议定义头文件
├── software_iic.h           # 软件I2C驱动头文件
├── software_iic.c           # 软件I2C驱动实现文件
└── hardware_iic.h           # 硬件I2C驱动头文件（已注释，可选）
└── hardware_iic.c           # 硬件I2C驱动实现文件（已注释，可选）
```

### 2. 应用层文件
```
User/App/
├── gray_app.h               # 灰度传感器应用层头文件
└── gray_app.c               # 灰度传感器应用层实现文件
```

### 3. 依赖文件
```
User/
├── mydefine.h               # 项目通用定义文件
└── App/usart_app.h          # 串口应用层（用于调试输出）
```

## 移植步骤

### 步骤1: 复制文件

将以下文件复制到新项目中：

1. **复制驱动文件**
   ```
   源路径: User/Module/grayscale/
   目标路径: [新项目]/Drivers/Grayscale/ 或 [新项目]/Components/Grayscale/
   
   文件列表:
   - gw_grayscale_sensor.h
   - software_iic.h
   - software_iic.c
   - hardware_iic.h (可选)
   - hardware_iic.c (可选)
   ```

2. **复制应用文件**
   ```
   源路径: User/App/
   目标路径: [新项目]/Application/ 或 [新项目]/App/
   
   文件列表:
   - gray_app.h
   - gray_app.c
   ```

### 步骤2: 配置GPIO引脚

#### 2.1 在STM32CubeMX中配置GPIO

1. 打开STM32CubeMX
2. 在Pinout & Configuration中配置GPIO:
   - PC4 → GPIO_Output (GRAY_SOFT_SCL)
   - PC5 → GPIO_Output (GRAY_SOFT_SDA)
3. GPIO配置参数:
   - Output Level: High
   - Mode: Output Open Drain
   - Pull-up/Pull-down: Pull-up
   - Maximum output speed: Low

#### 2.2 手动配置GPIO（如果不使用CubeMX）

在`main.h`中添加引脚定义：
```c
/* 灰度传感器软件I2C引脚定义 */
#define GRAY_SOFT_SCL_Pin GPIO_PIN_4
#define GRAY_SOFT_SCL_GPIO_Port GPIOC
#define GRAY_SOFT_SDA_Pin GPIO_PIN_5
#define GRAY_SOFT_SDA_GPIO_Port GPIOC
```

在GPIO初始化函数中添加配置代码：
```c
void MX_GPIO_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    
    /* GPIO Ports Clock Enable */
    __HAL_RCC_GPIOC_CLK_ENABLE();
    
    /* Configure GPIO pins : GRAY_SOFT_SCL_Pin GRAY_SOFT_SDA_Pin */
    GPIO_InitStruct.Pin = GRAY_SOFT_SCL_Pin|GRAY_SOFT_SDA_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_OD;
    GPIO_InitStruct.Pull = GPIO_PULLUP;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
    HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);
    
    /* Set initial state to HIGH */
    HAL_GPIO_WritePin(GPIOC, GRAY_SOFT_SCL_Pin|GRAY_SOFT_SDA_Pin, GPIO_PIN_SET);
}
```

### 步骤3: 修改包含路径

在新项目的编译器设置中添加包含路径：
```
[新项目]/Drivers/Grayscale
[新项目]/Application
```

或在Keil MDK中的C/C++选项卡的Include Paths中添加相应路径。

### 步骤4: 修改依赖关系

#### 4.1 修改software_iic.c中的包含文件

确保`software_iic.c`文件顶部的包含语句正确：
```c
#include "software_iic.h"
```

#### 4.2 修改gray_app.c中的包含文件

根据新项目结构修改包含路径：
```c
#include "gray_app.h"
#include "software_iic.h"
// 如果需要调试输出，包含串口相关头文件
// #include "usart_app.h"  // 根据实际情况调整
```

#### 4.3 创建mydefine.h文件（如果不存在）

在项目根目录创建`mydefine.h`文件：
```c
#ifndef __MYDEFINE_H
#define __MYDEFINE_H

#include <stdio.h>
#include <string.h>
#include <stdarg.h>
#include <math.h>
#include <stdint.h>
#include <stdbool.h>
#include "main.h"
#include "stm32f4xx_hal.h"

#endif
```

### 步骤5: 适配系统时钟

#### 5.1 修改延时函数

在`software_iic.c`中的`Delay_us`函数需要根据实际系统时钟频率调整：
```c
void Delay_us(uint32_t udelay)
{
    uint32_t startval, tickn, delays, wait;
    
    startval = SysTick->VAL;
    tickn = HAL_GetTick();
    delays = udelay * 168; // 168MHz系统时钟，根据实际时钟频率调整
    
    // ... 其余代码保持不变
}
```

**注意**: 如果您的系统时钟不是168MHz，请将`delays = udelay * 168;`中的168修改为您的实际系统时钟频率（MHz）。

### 步骤6: 初始化和使用

#### 6.1 在主程序中初始化

在`main.c`中添加初始化代码：
```c
#include "gray_app.h"

int main(void)
{
    // ... 其他初始化代码
    
    MX_GPIO_Init();         // 初始化GPIO
    Gray_Init();            // 初始化灰度传感器
    
    // ... 主循环
    while (1)
    {
        // 可以调用Gray_Task()或直接使用灰度传感器函数
        Gray_Task();        // 读取灰度传感器数据
        HAL_Delay(10);      // 适当延时
    }
}
```

#### 6.2 定时器中断调用（推荐）

如果使用定时器中断进行周期性数据读取：
```c
// 在定时器中断回调函数中调用
void HAL_TIM_PeriodElapsedCallback(TIM_HandleTypeDef *htim)
{
    if(htim->Instance == TIM2) // 假设使用TIM2，5ms中断
    {
        Gray_Task();  // 读取灰度传感器数据
    }
}
```

#### 6.3 基本使用示例

```c
#include "gray_app.h"

// 获取数字量数据
extern unsigned char Digtal;           // 8位数字量数据
extern float g_line_position_error;    // 循迹位置误差

void example_usage(void)
{
    // 初始化
    Gray_Init();
    
    // 在循环中读取数据
    while(1)
    {
        Gray_Task();  // 更新传感器数据
        
        // 使用数字量数据
        for(int i = 0; i < 8; i++)
        {
            uint8_t sensor_bit = (Digtal >> i) & 0x01;
            printf("Sensor %d: %d\n", i+1, sensor_bit);
        }
        
        // 使用位置误差进行控制
        printf("Line Position Error: %.2f\n", g_line_position_error);
        
        HAL_Delay(100);
    }
}
```

## 功能说明

### 1. 核心功能

#### 1.1 传感器连接检测
```c
unsigned char Ping(void);  // 返回0表示连接正常，1表示连接异常
```

#### 1.2 数字量数据读取
```c
unsigned char IIC_Get_Digtal(void);  // 返回8位数字量数据
```

#### 1.3 模拟量数据读取
```c
// 读取所有8路模拟量数据
unsigned char IIC_Get_Anolog(unsigned char *Result, unsigned char len);

// 读取单路模拟量数据 (Channel: 1-8)
unsigned char IIC_Get_Single_Anolog(unsigned char Channel);
```

### 2. 应用层功能

#### 2.1 全局变量
```c
extern unsigned char Digtal;              // 8位数字量数据（取反后）
extern float g_line_position_error;       // 循迹位置误差 (-4.0 ~ +4.0)
```

#### 2.2 权重配置
```c
// 8路传感器的权重配置（可根据实际需要调整）
float gray_weights[8] = {-4.0f, -3.0f, -2.0f, -1.0f, 1.0f, 2.0f, 3.0f, 4.0f};
```

### 3. 位置误差计算算法

灰度传感器通过加权平均算法计算循迹位置误差：
```c
// 计算加权平均位置误差
float weighted_sum = 0;
uint8_t black_line_count = 0;

for(uint8_t i = 0; i < 8; i++)
{
    if((Digtal>>i) & 0x01)  // 检测到黑线
    {
        weighted_sum += gray_weights[i];
        black_line_count++;
    }
}

if(black_line_count > 0)
    g_line_position_error = weighted_sum / (float)black_line_count;
```

## 注意事项

### 1. 硬件连接要求
```
灰度传感器模块    STM32F407
VCC          ->   3.3V/5V (根据模块规格)
GND          ->   GND
SCL          ->   PC4 (GRAY_SOFT_SCL)
SDA          ->   PC5 (GRAY_SOFT_SDA)
```

### 2. 软件I2C时序

- I2C通信速度: 约100kHz (通过10μs延时控制)
- 支持标准I2C协议时序
- 自动处理SDA方向切换（输入/输出模式）

### 3. 依赖清理

原工程中的`gray_app.c`包含了一些特定的外部依赖：
- `my_printf()` - 调试输出函数
- `huart1` - 串口句柄

移植时需要：
- 如果不需要调试输出，可以注释掉相关代码
- 或者提供相应的串口输出函数实现

### 4. 内存使用

- 静态变量占用: 约40字节
- 栈使用: 函数调用时临时占用约20字节
- 无动态内存分配

### 5. 性能特征

- 数据更新频率: 建议5-10ms
- I2C通信时间: 约2-3ms（8位数据）
- CPU占用率: 极低（<1%）

## 高级配置

### 1. 更改I2C地址

如果需要更改传感器I2C地址，修改`gw_grayscale_sensor.h`中的定义：
```c
#define GW_GRAY_ADDR_DEF 0x4C  // 根据实际传感器地址调整
```

### 2. 更改GPIO引脚

如果需要使用不同的GPIO引脚，修改`software_iic.h`中的定义：
```c
#define SDA_PIN GRAY_SOFT_SDA_Pin           // 修改为实际SDA引脚
#define SDA_PORT GRAY_SOFT_SDA_GPIO_Port    // 修改为实际SDA端口
#define SCL_PIN GRAY_SOFT_SCL_Pin           // 修改为实际SCL引脚
#define SCL_PORT GRAY_SOFT_SCL_GPIO_Port    // 修改为实际SCL端口
```

### 3. 调整权重配置

根据实际循迹需求调整传感器权重：
```c
// 示例：不同的权重配置
float gray_weights[8] = {-3.5f, -2.5f, -1.5f, -0.5f, 0.5f, 1.5f, 2.5f, 3.5f};
```

### 4. 使用硬件I2C（可选）

如果希望使用硬件I2C而不是软件I2C：
1. 取消注释`hardware_iic.h`和`hardware_iic.c`中的代码
2. 配置相应的I2C外设（如I2C2）
3. 修改`gray_app.c`中的包含文件为`#include "hardware_iic.h"`

## 测试验证

移植完成后，建议按以下步骤测试：

1. **基本通信测试**
   ```c
   Gray_Init();  // 查看串口输出是否显示连接成功
   ```

2. **数据读取测试**
   ```c
   Gray_Task();
   printf("Digital: 0x%02X\n", Digtal);
   ```

3. **位置误差测试**
   ```c
   printf("Position Error: %.2f\n", g_line_position_error);
   ```

## 常见问题

1. **无法检测到传感器**: 检查I2C地址、引脚配置和电源连接
2. **数据读取异常**: 检查软件I2C时序和系统时钟配置
3. **编译错误**: 检查包含路径和文件依赖关系
4. **位置误差计算异常**: 检查权重配置和算法逻辑

## 协议说明

### 1. 感为智能灰度传感器协议

该传感器支持多种工作模式：

#### 1.1 数字模式
- 命令: `0xDD`
- 返回: 8位数字量数据，每位代表一个传感器的黑白状态

#### 1.2 模拟模式
- 命令: `0xB0` (读取所有8路)
- 命令: `0xB1-0xB8` (读取单路，1-8)
- 返回: 对应的模拟量数值

#### 1.3 连接检测
- 命令: `0xAA`
- 正常返回: `0x66`

### 2. 数据处理说明

- 原始数据经过取反处理：`Digtal = ~temp`
- 这样处理后，检测到黑线时对应位为1，白色时为0
- 符合一般的逻辑习惯（黑线为有效信号）

## 总结

按照以上步骤，您应该能够成功将灰度传感器功能移植到新的STM32项目中。移植的核心是：

1. **正确配置软件I2C的GPIO引脚**
2. **处理文件依赖关系**
3. **适配系统时钟频率**
4. **根据需要调整权重配置**

该模块设计良好，资源占用少，适合在循迹小车等应用中使用。如果在移植过程中遇到问题，请检查硬件连接、引脚配置和系统时钟设置。
